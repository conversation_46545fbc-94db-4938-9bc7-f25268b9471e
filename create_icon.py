#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Create a simple icon for the PDF to Word Converter app
สร้าง icon สำหรับ PDF to Word Converter app
"""

try:
    from PIL import Image, ImageDraw, ImageFont
    PIL_AVAILABLE = True
except ImportError:
    PIL_AVAILABLE = False

import os


def create_simple_icon():
    """สร้าง icon แบบง่าย"""
    if not PIL_AVAILABLE:
        print("PIL/Pillow ไม่พร้อมใช้งาน - ข้าม การสร้าง icon")
        return False
    
    try:
        # สร้างภาพ 256x256 pixels
        size = 256
        img = Image.new('RGBA', (size, size), (255, 255, 255, 0))
        draw = ImageDraw.Draw(img)
        
        # วาดพื้นหลัง
        margin = 20
        draw.rounded_rectangle(
            [margin, margin, size-margin, size-margin],
            radius=30,
            fill=(52, 152, 219, 255),  # สีน้ำเงิน
            outline=(41, 128, 185, 255),
            width=3
        )
        
        # วาดสัญลักษณ์ PDF (สีแดง)
        pdf_rect = [40, 60, 120, 140]
        draw.rounded_rectangle(pdf_rect, radius=10, fill=(231, 76, 60, 255))
        
        # วาดลูกศร
        arrow_points = [
            (130, 100), (160, 80), (160, 90),
            (180, 90), (180, 110), (160, 110), (160, 120)
        ]
        draw.polygon(arrow_points, fill=(255, 255, 255, 255))
        
        # วาดสัญลักษณ์ Word (สีน้ำเงินเข้ม)
        word_rect = [190, 60, 270, 140]
        draw.rounded_rectangle(word_rect, radius=10, fill=(52, 73, 94, 255))
        
        # เพิ่มข้อความ
        try:
            # ลองใช้ font ที่มีอยู่ในระบบ
            font_size = 24
            font = ImageFont.truetype("arial.ttf", font_size)
        except:
            try:
                font = ImageFont.truetype("calibri.ttf", font_size)
            except:
                font = ImageFont.load_default()
        
        # เขียน "PDF" และ "DOC"
        draw.text((65, 90), "PDF", fill=(255, 255, 255, 255), font=font, anchor="mm")
        draw.text((230, 90), "DOC", fill=(255, 255, 255, 255), font=font, anchor="mm")
        
        # เขียนข้อความด้านล่าง
        try:
            small_font = ImageFont.truetype("arial.ttf", 16)
        except:
            small_font = font
        
        draw.text((size//2, 180), "PDF to Word", fill=(255, 255, 255, 255), 
                 font=small_font, anchor="mm")
        draw.text((size//2, 200), "Converter", fill=(255, 255, 255, 255), 
                 font=small_font, anchor="mm")
        
        # บันทึกเป็น ICO file
        icon_path = "app_icon.ico"
        img.save(icon_path, format='ICO', sizes=[(256, 256), (128, 128), (64, 64), (32, 32), (16, 16)])
        
        print(f"สร้าง icon สำเร็จ: {icon_path}")
        return True
        
    except Exception as e:
        print(f"เกิดข้อผิดพลาดในการสร้าง icon: {e}")
        return False


def install_pillow():
    """ติดตั้ง Pillow สำหรับสร้าง icon"""
    import subprocess
    import sys
    
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "Pillow"])
        print("ติดตั้ง Pillow สำเร็จ")
        return True
    except subprocess.CalledProcessError:
        print("ไม่สามารถติดตั้ง Pillow ได้")
        return False


if __name__ == "__main__":
    print("สร้าง icon สำหรับ PDF to Word Converter...")
    
    if not PIL_AVAILABLE:
        print("กำลังติดตั้ง Pillow...")
        if install_pillow():
            # ลองใหม่หลังจากติดตั้ง
            try:
                from PIL import Image, ImageDraw, ImageFont
                PIL_AVAILABLE = True
            except ImportError:
                print("ไม่สามารถใช้ Pillow ได้หลังจากติดตั้ง")
    
    if PIL_AVAILABLE:
        create_simple_icon()
    else:
        print("ข้ามการสร้าง icon - จะใช้ icon เริ่มต้นของ Windows")
