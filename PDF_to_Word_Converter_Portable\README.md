# PDF to Word Converter

โปรแกรมแปลงไฟล์ PDF เป็น Word Document (.docx) ด้วย Python

## คุณสมบัติ

- แปลงไฟล์ PDF เป็น Word Document (.docx)
- รองรับการใช้งานแบบ GUI (Graphical User Interface)
- รองรับการใช้งานแบบ Command Line
- แสดงความคืบหน้าในการแปลงไฟล์
- Log การทำงานแบบ Real-time
- รองรับภาษาไทย

## การติดตั้ง

### 1. ติดตั้ง Python Dependencies

```bash
pip install -r requirements.txt
```

หรือติดตั้งแต่ละ package:

```bash
pip install pdf2docx python-docx PyPDF2
```

### 2. ตรวจสอบการติดตั้ง

```bash
python pdf_to_word_converter.py
```

## การใช้งาน

### แบบ GUI (แนะนำ)

เรียกใช้โปรแกรมโดยไม่ใส่ parameter:

```bash
python pdf_to_word_converter.py
```

จากนั้น:
1. คลิก "เลือกไฟล์" เพื่อเลือกไฟล์ PDF ที่ต้องการแปลง
2. คลิก "เลือกโฟลเดอร์" เพื่อเลือกโฟลเดอร์ที่ต้องการบันทึกไฟล์ Word
3. คลิก "แปลงไฟล์" เพื่อเริ่มการแปลง
4. รอจนกว่าการแปลงจะเสร็จสิ้น

### แบบ Command Line

แปลงไฟล์เดียว:
```bash
python pdf_to_word_converter.py "path/to/your/file.pdf"
```

แปลงไฟล์และกำหนดชื่อไฟล์ output:
```bash
python pdf_to_word_converter.py "path/to/your/file.pdf" "path/to/output/file.docx"
```

## ตัวอย่างการใช้งาน

### GUI Mode
```bash
python pdf_to_word_converter.py
```

### Command Line Mode
```bash
# แปลงไฟล์ document.pdf ในโฟลเดอร์เดียวกัน
python pdf_to_word_converter.py document.pdf

# แปลงไฟล์และบันทึกในโฟลเดอร์อื่น
python pdf_to_word_converter.py document.pdf output/document.docx
```

## ข้อกำหนดระบบ

- Python 3.7 หรือใหม่กว่า
- Windows, macOS, หรือ Linux
- RAM อย่างน้อย 2GB (สำหรับไฟล์ PDF ขนาดใหญ่)

## ข้อจำกัด

- ไฟล์ PDF ที่มีการเข้ารหัส (Password Protected) อาจไม่สามารถแปลงได้
- การแปลงไฟล์ที่มีรูปภาพหรือตารางซับซ้อนอาจไม่สมบูรณ์ 100%
- ไฟล์ PDF ขนาดใหญ่อาจใช้เวลาในการแปลงนาน

## การแก้ไขปัญหา

### ปัญหาที่พบบ่อย

1. **ModuleNotFoundError**: ตรวจสอบว่าได้ติดตั้ง dependencies ครบถ้วนแล้ว
2. **Permission Error**: ตรวจสอบสิทธิ์ในการเขียนไฟล์ในโฟลเดอร์ปลายทาง
3. **Memory Error**: ลองแปลงไฟล์ที่มีขนาดเล็กกว่า

### การติดต่อ

หากพบปัญหาหรือต้องการความช่วยเหลือ กรุณาสร้าง Issue ใน Repository นี้

## License

MIT License - ใช้งานได้อย่างอิสระ
