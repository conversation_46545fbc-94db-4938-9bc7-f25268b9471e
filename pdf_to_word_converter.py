#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PDF to Word Converter
แปลงไฟล์ PDF เป็น Word Document (.docx)
"""

import os
import sys
from pathlib import Path
from pdf2docx import Converter
import tkinter as tk
from tkinter import filedialog, messagebox, ttk
import threading


class PDFToWordConverter:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("PDF to Word Converter")
        self.root.geometry("600x400")
        self.root.resizable(True, True)
        
        # Variables
        self.pdf_file_path = tk.StringVar()
        self.output_folder = tk.StringVar()
        self.progress_var = tk.DoubleVar()
        
        self.setup_ui()
        
    def setup_ui(self):
        """สร้าง User Interface"""
        # Main frame
        main_frame = ttk.Frame(self.root, padding="20")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Title
        title_label = ttk.Label(main_frame, text="PDF to Word Converter", 
                               font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        # PDF File Selection
        ttk.Label(main_frame, text="เลือกไฟล์ PDF:").grid(row=1, column=0, sticky=tk.W, pady=5)
        ttk.Entry(main_frame, textvariable=self.pdf_file_path, width=50).grid(row=1, column=1, padx=5, pady=5)
        ttk.Button(main_frame, text="เลือกไฟล์", command=self.select_pdf_file).grid(row=1, column=2, pady=5)
        
        # Output Folder Selection
        ttk.Label(main_frame, text="โฟลเดอร์ปลายทาง:").grid(row=2, column=0, sticky=tk.W, pady=5)
        ttk.Entry(main_frame, textvariable=self.output_folder, width=50).grid(row=2, column=1, padx=5, pady=5)
        ttk.Button(main_frame, text="เลือกโฟลเดอร์", command=self.select_output_folder).grid(row=2, column=2, pady=5)
        
        # Progress Bar
        ttk.Label(main_frame, text="ความคืบหนา:").grid(row=3, column=0, sticky=tk.W, pady=(20, 5))
        self.progress_bar = ttk.Progressbar(main_frame, variable=self.progress_var, 
                                          maximum=100, length=300)
        self.progress_bar.grid(row=3, column=1, columnspan=2, pady=(20, 5), sticky=(tk.W, tk.E))
        
        # Convert Button
        self.convert_button = ttk.Button(main_frame, text="แปลงไฟล์", 
                                       command=self.start_conversion, style="Accent.TButton")
        self.convert_button.grid(row=4, column=0, columnspan=3, pady=20)
        
        # Status Label
        self.status_label = ttk.Label(main_frame, text="พร้อมใช้งาน", foreground="green")
        self.status_label.grid(row=5, column=0, columnspan=3, pady=5)
        
        # Log Text Area
        ttk.Label(main_frame, text="Log:").grid(row=6, column=0, sticky=tk.W, pady=(10, 5))
        self.log_text = tk.Text(main_frame, height=8, width=70)
        self.log_text.grid(row=7, column=0, columnspan=3, pady=5, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Scrollbar for log
        scrollbar = ttk.Scrollbar(main_frame, orient="vertical", command=self.log_text.yview)
        scrollbar.grid(row=7, column=3, sticky=(tk.N, tk.S))
        self.log_text.configure(yscrollcommand=scrollbar.set)
        
        # Configure grid weights
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(7, weight=1)
        
    def log_message(self, message):
        """เพิ่มข้อความใน log"""
        self.log_text.insert(tk.END, f"{message}\n")
        self.log_text.see(tk.END)
        self.root.update_idletasks()
        
    def select_pdf_file(self):
        """เลือกไฟล์ PDF"""
        file_path = filedialog.askopenfilename(
            title="เลือกไฟล์ PDF",
            filetypes=[("PDF files", "*.pdf"), ("All files", "*.*")]
        )
        if file_path:
            self.pdf_file_path.set(file_path)
            self.log_message(f"เลือกไฟล์: {file_path}")
            
            # Set default output folder to same directory as PDF
            default_output = os.path.dirname(file_path)
            if not self.output_folder.get():
                self.output_folder.set(default_output)
                
    def select_output_folder(self):
        """เลือกโฟลเดอร์ปลายทาง"""
        folder_path = filedialog.askdirectory(title="เลือกโฟลเดอร์ปลายทาง")
        if folder_path:
            self.output_folder.set(folder_path)
            self.log_message(f"เลือกโฟลเดอร์ปลายทาง: {folder_path}")
            
    def start_conversion(self):
        """เริ่มการแปลงไฟล์ในเธรดแยก"""
        if not self.pdf_file_path.get():
            messagebox.showerror("ข้อผิดพลาด", "กรุณาเลือกไฟล์ PDF")
            return
            
        if not self.output_folder.get():
            messagebox.showerror("ข้อผิดพลาด", "กรุณาเลือกโฟลเดอร์ปลายทาง")
            return
            
        # Disable convert button during conversion
        self.convert_button.config(state="disabled")
        self.status_label.config(text="กำลังแปลงไฟล์...", foreground="orange")
        
        # Start conversion in separate thread
        conversion_thread = threading.Thread(target=self.convert_pdf_to_word)
        conversion_thread.daemon = True
        conversion_thread.start()
        
    def convert_pdf_to_word(self):
        """แปลงไฟล์ PDF เป็น Word"""
        try:
            pdf_path = self.pdf_file_path.get()
            output_dir = self.output_folder.get()
            
            # Create output filename
            pdf_name = Path(pdf_path).stem
            output_path = os.path.join(output_dir, f"{pdf_name}.docx")
            
            self.log_message(f"เริ่มแปลงไฟล์: {pdf_path}")
            self.log_message(f"ไฟล์ปลายทาง: {output_path}")
            
            # Update progress
            self.progress_var.set(10)
            
            # Convert PDF to Word
            cv = Converter(pdf_path)
            self.progress_var.set(30)
            
            cv.convert(output_path, start=0, end=None)
            self.progress_var.set(80)
            
            cv.close()
            self.progress_var.set(100)
            
            self.log_message("แปลงไฟล์สำเร็จ!")
            self.status_label.config(text="แปลงไฟล์สำเร็จ!", foreground="green")
            
            # Show success message
            messagebox.showinfo("สำเร็จ", f"แปลงไฟล์สำเร็จ!\nไฟล์ถูกบันทึกที่: {output_path}")
            
        except Exception as e:
            error_msg = f"เกิดข้อผิดพลาด: {str(e)}"
            self.log_message(error_msg)
            self.status_label.config(text="เกิดข้อผิดพลาด", foreground="red")
            messagebox.showerror("ข้อผิดพลาด", error_msg)
            
        finally:
            # Re-enable convert button
            self.convert_button.config(state="normal")
            self.progress_var.set(0)
            
    def run(self):
        """เริ่มโปรแกรม"""
        self.root.mainloop()


def convert_pdf_to_word_cli(pdf_path, output_path=None):
    """
    แปลงไฟล์ PDF เป็น Word ผ่าน Command Line
    
    Args:
        pdf_path (str): path ของไฟล์ PDF
        output_path (str): path ของไฟล์ Word ที่ต้องการ (optional)
    """
    try:
        if not os.path.exists(pdf_path):
            print(f"ไม่พบไฟล์: {pdf_path}")
            return False
            
        if output_path is None:
            # สร้างชื่อไฟล์ output อัตโนมัติ
            pdf_name = Path(pdf_path).stem
            output_dir = os.path.dirname(pdf_path)
            output_path = os.path.join(output_dir, f"{pdf_name}.docx")
            
        print(f"กำลังแปลงไฟล์: {pdf_path}")
        print(f"ไฟล์ปลายทาง: {output_path}")
        
        # แปลงไฟล์
        cv = Converter(pdf_path)
        cv.convert(output_path, start=0, end=None)
        cv.close()
        
        print("แปลงไฟล์สำเร็จ!")
        return True
        
    except Exception as e:
        print(f"เกิดข้อผิดพลาด: {str(e)}")
        return False


if __name__ == "__main__":
    if len(sys.argv) > 1:
        # Command line mode
        pdf_file = sys.argv[1]
        output_file = sys.argv[2] if len(sys.argv) > 2 else None
        convert_pdf_to_word_cli(pdf_file, output_file)
    else:
        # GUI mode
        app = PDFToWordConverter()
        app.run()
