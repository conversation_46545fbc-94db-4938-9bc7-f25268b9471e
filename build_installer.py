#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Build installer for PDF to Word Converter
สร้าง installer สำหรับ PDF to Word Converter
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path


def find_inno_setup():
    """ค้นหา Inno Setup Compiler"""
    possible_paths = [
        r"C:\Program Files (x86)\Inno Setup 6\ISCC.exe",
        r"C:\Program Files\Inno Setup 6\ISCC.exe",
        r"C:\Program Files (x86)\Inno Setup 5\ISCC.exe",
        r"C:\Program Files\Inno Setup 5\ISCC.exe",
    ]
    
    for path in possible_paths:
        if os.path.exists(path):
            return path
    
    return None


def check_requirements():
    """ตรวจสอบความพร้อมสำหรับการ build"""
    print("ตรวจสอบความพร้อม...")
    
    # ตรวจสอบไฟล์ executable
    exe_path = "dist/PDF_to_Word_Converter.exe"
    if not os.path.exists(exe_path):
        print(f"❌ ไม่พบไฟล์ executable: {exe_path}")
        print("กรุณารัน: py -m PyInstaller pdf_to_word_converter.spec")
        return False
    else:
        print(f"✅ พบไฟล์ executable: {exe_path}")
    
    # ตรวจสอบไฟล์ icon
    icon_path = "app_icon.ico"
    if not os.path.exists(icon_path):
        print(f"❌ ไม่พบไฟล์ icon: {icon_path}")
        print("กรุณารัน: py create_icon.py")
        return False
    else:
        print(f"✅ พบไฟล์ icon: {icon_path}")
    
    # ตรวจสอบไฟล์ setup script
    setup_path = "installer_setup.iss"
    if not os.path.exists(setup_path):
        print(f"❌ ไม่พบไฟล์ setup script: {setup_path}")
        return False
    else:
        print(f"✅ พบไฟล์ setup script: {setup_path}")
    
    return True


def build_installer():
    """สร้าง installer"""
    print("\n" + "="*50)
    print("สร้าง installer สำหรับ PDF to Word Converter")
    print("="*50)
    
    # ตรวจสอบความพร้อม
    if not check_requirements():
        return False
    
    # ค้นหา Inno Setup
    iscc_path = find_inno_setup()
    if not iscc_path:
        print("\n❌ ไม่พบ Inno Setup Compiler")
        print("กรุณาดาวน์โหลดและติดตั้ง Inno Setup จาก:")
        print("https://jrsoftware.org/isdl.php")
        print("\nหรือใช้วิธีอื่น:")
        print("1. เปิดไฟล์ installer_setup.iss ด้วย Inno Setup")
        print("2. คลิก Build > Compile")
        return False
    
    print(f"\n✅ พบ Inno Setup: {iscc_path}")
    
    # สร้างโฟลเดอร์ output
    output_dir = "installer_output"
    os.makedirs(output_dir, exist_ok=True)
    
    # รัน Inno Setup Compiler
    print("\n🔨 กำลังสร้าง installer...")
    try:
        result = subprocess.run([
            iscc_path,
            "installer_setup.iss"
        ], capture_output=True, text=True, cwd=os.getcwd())
        
        if result.returncode == 0:
            print("✅ สร้าง installer สำเร็จ!")
            
            # ค้นหาไฟล์ installer ที่สร้างขึ้น
            installer_files = list(Path(output_dir).glob("*.exe"))
            if installer_files:
                installer_path = installer_files[0]
                file_size = installer_path.stat().st_size / (1024 * 1024)  # MB
                print(f"📦 ไฟล์ installer: {installer_path}")
                print(f"📏 ขนาดไฟล์: {file_size:.1f} MB")
                
                # แสดงข้อมูลเพิ่มเติม
                print(f"\n🎉 สำเร็จ! สามารถแจกจ่ายไฟล์ installer ได้แล้ว")
                print(f"📁 ตำแหน่งไฟล์: {os.path.abspath(installer_path)}")
                
                return True
            else:
                print("❌ ไม่พบไฟล์ installer ที่สร้างขึ้น")
                return False
        else:
            print("❌ เกิดข้อผิดพลาดในการสร้าง installer")
            print("STDOUT:", result.stdout)
            print("STDERR:", result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาด: {e}")
        return False


def create_portable_version():
    """สร้าง portable version"""
    print("\n📦 สร้าง portable version...")
    
    portable_dir = "portable_version"
    if os.path.exists(portable_dir):
        shutil.rmtree(portable_dir)
    
    os.makedirs(portable_dir)
    
    # คัดลอกไฟล์ที่จำเป็น
    files_to_copy = [
        ("dist/PDF_to_Word_Converter.exe", "PDF_to_Word_Converter.exe"),
        ("README.md", "README.md"),
        ("app_icon.ico", "app_icon.ico")
    ]
    
    for src, dst in files_to_copy:
        if os.path.exists(src):
            shutil.copy2(src, os.path.join(portable_dir, dst))
            print(f"✅ คัดลอก: {src} -> {dst}")
    
    # สร้างไฟล์ README สำหรับ portable version
    portable_readme = os.path.join(portable_dir, "PORTABLE_README.txt")
    with open(portable_readme, 'w', encoding='utf-8') as f:
        f.write("PDF to Word Converter - Portable Version\n")
        f.write("=" * 45 + "\n\n")
        f.write("วิธีใช้งาน:\n")
        f.write("1. เปิดไฟล์ PDF_to_Word_Converter.exe\n")
        f.write("2. เลือกไฟล์ PDF ที่ต้องการแปลง\n")
        f.write("3. เลือกโฟลเดอร์ปลายทาง\n")
        f.write("4. คลิก 'แปลงไฟล์'\n\n")
        f.write("หมายเหตุ:\n")
        f.write("- ไม่ต้องติดตั้ง สามารถใช้งานได้ทันที\n")
        f.write("- สามารถคัดลอกไปใช้ในเครื่องอื่นได้\n")
        f.write("- ไฟล์ขนาดใหญ่อาจใช้เวลาในการแปลงนาน\n")
    
    print(f"✅ สร้าง portable version สำเร็จ: {portable_dir}/")
    
    # สร้างไฟล์ zip
    try:
        shutil.make_archive("PDF_to_Word_Converter_Portable", 'zip', portable_dir)
        print("✅ สร้างไฟล์ ZIP สำเร็จ: PDF_to_Word_Converter_Portable.zip")
    except Exception as e:
        print(f"❌ ไม่สามารถสร้างไฟล์ ZIP ได้: {e}")


def main():
    """ฟังก์ชันหลัก"""
    print("PDF to Word Converter - Build Installer")
    print("=" * 50)
    
    # สร้าง installer
    installer_success = build_installer()
    
    # สร้าง portable version
    create_portable_version()
    
    print("\n" + "="*50)
    if installer_success:
        print("🎉 สำเร็จ! ได้ไฟล์ทั้งหมด:")
        print("1. ไฟล์ installer (.exe) - สำหรับติดตั้งในเครื่อง")
        print("2. ไฟล์ portable (.zip) - สำหรับใช้งานโดยไม่ต้องติดตั้ง")
    else:
        print("⚠️  สร้าง installer ไม่สำเร็จ แต่มี portable version ให้ใช้")
    
    print("\nสามารถแจกจ่ายไฟล์เหล่านี้ได้แล้ว!")


if __name__ == "__main__":
    main()
