#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test script for PDF to Word Converter
สคริปต์ทดสอบสำหรับ PDF to Word Converter
"""

import os
import sys
import unittest
from unittest.mock import patch, MagicMock
import tempfile
from pathlib import Path

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from pdf_to_word_converter import convert_pdf_to_word_cli, PDFToWordConverter


class TestPDFToWordConverter(unittest.TestCase):
    """Test cases for PDF to Word Converter"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.test_dir = tempfile.mkdtemp()
        self.test_pdf = os.path.join(self.test_dir, "test.pdf")
        self.test_docx = os.path.join(self.test_dir, "test.docx")
        
    def tearDown(self):
        """Clean up test fixtures"""
        # Clean up test files
        for file_path in [self.test_pdf, self.test_docx]:
            if os.path.exists(file_path):
                try:
                    os.remove(file_path)
                except:
                    pass
        
        try:
            os.rmdir(self.test_dir)
        except:
            pass
    
    def test_file_not_exists(self):
        """Test handling of non-existent PDF file"""
        result = convert_pdf_to_word_cli("non_existent_file.pdf")
        self.assertFalse(result)
    
    @patch('pdf_to_word_converter.Converter')
    def test_successful_conversion(self, mock_converter):
        """Test successful PDF to Word conversion"""
        # Create a dummy PDF file
        with open(self.test_pdf, 'w') as f:
            f.write("dummy pdf content")
        
        # Mock the Converter
        mock_cv = MagicMock()
        mock_converter.return_value = mock_cv
        
        # Test conversion
        result = convert_pdf_to_word_cli(self.test_pdf, self.test_docx)
        
        # Verify
        self.assertTrue(result)
        mock_converter.assert_called_once_with(self.test_pdf)
        mock_cv.convert.assert_called_once()
        mock_cv.close.assert_called_once()
    
    @patch('pdf_to_word_converter.Converter')
    def test_conversion_with_auto_output_name(self, mock_converter):
        """Test conversion with automatic output filename generation"""
        # Create a dummy PDF file
        with open(self.test_pdf, 'w') as f:
            f.write("dummy pdf content")
        
        # Mock the Converter
        mock_cv = MagicMock()
        mock_converter.return_value = mock_cv
        
        # Test conversion without specifying output path
        result = convert_pdf_to_word_cli(self.test_pdf)
        
        # Verify
        self.assertTrue(result)
        mock_converter.assert_called_once_with(self.test_pdf)
        mock_cv.convert.assert_called_once()
        mock_cv.close.assert_called_once()
    
    @patch('pdf_to_word_converter.Converter')
    def test_conversion_error(self, mock_converter):
        """Test handling of conversion errors"""
        # Create a dummy PDF file
        with open(self.test_pdf, 'w') as f:
            f.write("dummy pdf content")
        
        # Mock the Converter to raise an exception
        mock_converter.side_effect = Exception("Conversion failed")
        
        # Test conversion
        result = convert_pdf_to_word_cli(self.test_pdf, self.test_docx)
        
        # Verify
        self.assertFalse(result)
    
    def test_gui_initialization(self):
        """Test GUI initialization"""
        try:
            # This might fail in headless environments
            app = PDFToWordConverter()
            self.assertIsNotNone(app.root)
            self.assertIsNotNone(app.pdf_file_path)
            self.assertIsNotNone(app.output_folder)
            app.root.destroy()  # Clean up
        except Exception as e:
            # Skip GUI tests in headless environments
            self.skipTest(f"GUI test skipped due to: {e}")


class TestUtilityFunctions(unittest.TestCase):
    """Test utility functions"""
    
    def test_path_handling(self):
        """Test path handling functions"""
        test_path = "/path/to/test.pdf"
        expected_stem = "test"
        actual_stem = Path(test_path).stem
        self.assertEqual(expected_stem, actual_stem)
    
    def test_file_extension_handling(self):
        """Test file extension handling"""
        pdf_path = "document.pdf"
        expected_docx = "document.docx"
        
        pdf_stem = Path(pdf_path).stem
        actual_docx = f"{pdf_stem}.docx"
        
        self.assertEqual(expected_docx, actual_docx)


def run_tests():
    """Run all tests"""
    print("Running PDF to Word Converter Tests...")
    print("=" * 50)
    
    # Create test suite
    loader = unittest.TestLoader()
    suite = unittest.TestSuite()
    
    # Add test cases
    suite.addTests(loader.loadTestsFromTestCase(TestPDFToWordConverter))
    suite.addTests(loader.loadTestsFromTestCase(TestUtilityFunctions))
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # Print summary
    print("\n" + "=" * 50)
    print(f"Tests run: {result.testsRun}")
    print(f"Failures: {len(result.failures)}")
    print(f"Errors: {len(result.errors)}")
    
    if result.failures:
        print("\nFailures:")
        for test, traceback in result.failures:
            print(f"- {test}: {traceback}")
    
    if result.errors:
        print("\nErrors:")
        for test, traceback in result.errors:
            print(f"- {test}: {traceback}")
    
    success = len(result.failures) == 0 and len(result.errors) == 0
    print(f"\nOverall result: {'PASSED' if success else 'FAILED'}")
    
    return success


if __name__ == "__main__":
    run_tests()
