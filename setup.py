from setuptools import setup, find_packages

with open("README.md", "r", encoding="utf-8") as fh:
    long_description = fh.read()

setup(
    name="pdf-to-word-converter",
    version="1.0.0",
    author="Your Name",
    author_email="<EMAIL>",
    description="แปลงไฟล์ PDF เป็น Word Document (.docx)",
    long_description=long_description,
    long_description_content_type="text/markdown",
    url="https://github.com/yourusername/pdf-to-word-converter",
    packages=find_packages(),
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: End Users/Desktop",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.7",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
    ],
    python_requires=">=3.7",
    install_requires=[
        "pdf2docx>=0.5.8",
        "python-docx>=0.8.11",
        "PyPDF2>=3.0.1",
    ],
    entry_points={
        "console_scripts": [
            "pdf2word=pdf_to_word_converter:main",
        ],
    },
)
