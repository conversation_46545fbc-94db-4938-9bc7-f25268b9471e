#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Quick launcher for PDF to Word Converter GUI
เปิดโปรแกรม PDF to Word Converter แบบ GUI
"""

import sys
import os

# Add current directory to Python path
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

try:
    from pdf_to_word_converter import PDFToWordConverter
    
    def main():
        """เริ่มโปรแกรม GUI"""
        print("เริ่มโปรแกรม PDF to Word Converter...")
        app = PDFToWordConverter()
        app.run()
    
    if __name__ == "__main__":
        main()
        
except ImportError as e:
    print(f"ข้อผิดพลาด: ไม่สามารถ import module ได้ - {e}")
    print("กรุณาติดตั้ง dependencies ก่อน:")
    print("pip install -r requirements.txt")
    sys.exit(1)
except Exception as e:
    print(f"เกิดข้อผิดพลาด: {e}")
    sys.exit(1)
