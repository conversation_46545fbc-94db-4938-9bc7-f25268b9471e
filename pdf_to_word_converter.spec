# -*- mode: python ; coding: utf-8 -*-

import sys
import os
from pathlib import Path

# Get the current directory
current_dir = os.getcwd()

# Define the main script
main_script = os.path.join(current_dir, 'pdf_to_word_converter.py')

# Hidden imports that PyInstaller might miss
hidden_imports = [
    'pdf2docx',
    'pdf2docx.converter',
    'pdf2docx.common',
    'pdf2docx.layout',
    'pdf2docx.page',
    'pdf2docx.text',
    'pdf2docx.image',
    'pdf2docx.table',
    'pdf2docx.shape',
    'docx',
    'docx.shared',
    'docx.enum',
    'docx.oxml',
    'PyPDF2',
    'fitz',  # PyMuPDF
    'numpy',
    'cv2',  # opencv-python-headless
    'fonttools',
    'fire',
    'lxml',
    'typing_extensions',
    'tkinter',
    'tkinter.filedialog',
    'tkinter.messagebox',
    'tkinter.ttk',
    'threading',
    'pathlib',
    'tempfile',
    'unittest',
    'unittest.mock'
]

# Data files to include
datas = []

# Binary files to include
binaries = []

# Excluded modules
excludes = [
    'matplotlib',
    'scipy',
    'pandas',
    'jupyter',
    'IPython',
    'notebook',
    'pytest',
    'sphinx',
    'setuptools',
    'distutils'
]

a = Analysis(
    [main_script],
    pathex=[current_dir],
    binaries=binaries,
    datas=datas,
    hiddenimports=hidden_imports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=excludes,
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=None,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=None)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='PDF_to_Word_Converter',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,  # Set to False for GUI app
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=os.path.join(current_dir, 'app_icon.ico'),
    version=None,  # Add version info file here if you have one
)

# Optional: Create a directory distribution instead of a single file
# Uncomment the following lines if you prefer a directory distribution:

# coll = COLLECT(
#     exe,
#     a.binaries,
#     a.zipfiles,
#     a.datas,
#     strip=False,
#     upx=True,
#     upx_exclude=[],
#     name='PDF_to_Word_Converter'
# )
