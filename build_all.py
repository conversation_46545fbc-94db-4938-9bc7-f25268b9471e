#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Build All - สร้าง executable และ installer ทั้งหมดในครั้งเดียว
Build everything for PDF to Word Converter
"""

import os
import sys
import subprocess
import time
from pathlib import Path


def print_step(step_num, total_steps, description):
    """แสดงขั้นตอนการทำงาน"""
    print(f"\n{'='*60}")
    print(f"ขั้นตอนที่ {step_num}/{total_steps}: {description}")
    print('='*60)


def run_command(command, description, cwd=None):
    """รันคำสั่งและแสดงผล"""
    print(f"🔨 {description}")
    print(f"💻 คำสั่ง: {command}")
    
    try:
        if cwd is None:
            cwd = os.getcwd()
            
        result = subprocess.run(
            command, 
            shell=True, 
            capture_output=True, 
            text=True, 
            cwd=cwd
        )
        
        if result.returncode == 0:
            print(f"✅ {description} สำเร็จ!")
            if result.stdout.strip():
                print("📄 Output:")
                print(result.stdout.strip())
            return True
        else:
            print(f"❌ {description} ล้มเหลว!")
            if result.stderr.strip():
                print("🚨 Error:")
                print(result.stderr.strip())
            if result.stdout.strip():
                print("📄 Output:")
                print(result.stdout.strip())
            return False
            
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาด: {e}")
        return False


def check_python():
    """ตรวจสอบ Python"""
    try:
        result = subprocess.run([sys.executable, "--version"], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            version = result.stdout.strip()
            print(f"✅ Python: {version}")
            return True
        else:
            print("❌ ไม่พบ Python")
            return False
    except:
        print("❌ ไม่สามารถตรวจสอบ Python ได้")
        return False


def main():
    """ฟังก์ชันหลัก"""
    start_time = time.time()
    
    print("🚀 PDF to Word Converter - Build All")
    print("สร้าง executable และ installer ทั้งหมดในครั้งเดียว")
    print("="*60)
    
    # ตรวจสอบ Python
    if not check_python():
        print("❌ กรุณาติดตั้ง Python ก่อน")
        return False
    
    total_steps = 7
    current_step = 0
    
    # ขั้นตอนที่ 1: ติดตั้ง dependencies
    current_step += 1
    print_step(current_step, total_steps, "ติดตั้ง Dependencies")
    if not run_command("py install_dependencies.py", "ติดตั้ง dependencies"):
        print("⚠️  หากติดตั้งไม่สำเร็จ ให้รันคำสั่งนี้ด้วยตนเอง:")
        print("py install_dependencies.py")
    
    # ขั้นตอนที่ 2: ติดตั้ง PyInstaller
    current_step += 1
    print_step(current_step, total_steps, "ติดตั้ง PyInstaller")
    if not run_command("py -m pip install pyinstaller", "ติดตั้ง PyInstaller"):
        print("❌ ไม่สามารถติดตั้ง PyInstaller ได้")
        return False
    
    # ขั้นตอนที่ 3: สร้าง Icon
    current_step += 1
    print_step(current_step, total_steps, "สร้าง Icon")
    if not run_command("py create_icon.py", "สร้าง icon"):
        print("⚠️  ไม่สามารถสร้าง icon ได้ จะใช้ icon เริ่มต้น")
    
    # ขั้นตอนที่ 4: ทดสอบโปรแกรม
    current_step += 1
    print_step(current_step, total_steps, "ทดสอบโปรแกรม")
    if not run_command("py test_converter.py", "ทดสอบโปรแกรม"):
        print("⚠️  การทดสอบไม่ผ่าน แต่จะดำเนินการต่อ")
    
    # ขั้นตอนที่ 5: สร้าง Executable
    current_step += 1
    print_step(current_step, total_steps, "สร้าง Executable")
    if not run_command("py -m PyInstaller pdf_to_word_converter.spec", "สร้าง executable"):
        print("❌ ไม่สามารถสร้าง executable ได้")
        return False
    
    # ตรวจสอบว่าไฟล์ executable ถูกสร้างขึ้น
    exe_path = "dist/PDF_to_Word_Converter.exe"
    if os.path.exists(exe_path):
        file_size = os.path.getsize(exe_path) / (1024 * 1024)  # MB
        print(f"✅ สร้าง executable สำเร็จ: {exe_path} ({file_size:.1f} MB)")
    else:
        print(f"❌ ไม่พบไฟล์ executable: {exe_path}")
        return False
    
    # ขั้นตอนที่ 6: สร้าง Installer และ Portable
    current_step += 1
    print_step(current_step, total_steps, "สร้าง Installer และ Portable Version")
    if not run_command("py build_installer.py", "สร้าง installer และ portable version"):
        print("⚠️  อาจไม่สามารถสร้าง installer ได้ (ต้องมี Inno Setup)")
        print("แต่ portable version ควรสร้างสำเร็จ")
    
    # ขั้นตอนที่ 7: สรุปผล
    current_step += 1
    print_step(current_step, total_steps, "สรุปผลการ Build")
    
    # ตรวจสอบไฟล์ที่สร้างขึ้น
    files_created = []
    
    # Executable
    if os.path.exists("dist/PDF_to_Word_Converter.exe"):
        size = os.path.getsize("dist/PDF_to_Word_Converter.exe") / (1024 * 1024)
        files_created.append(f"✅ Executable: dist/PDF_to_Word_Converter.exe ({size:.1f} MB)")
    
    # Portable ZIP
    if os.path.exists("PDF_to_Word_Converter_Portable.zip"):
        size = os.path.getsize("PDF_to_Word_Converter_Portable.zip") / (1024 * 1024)
        files_created.append(f"✅ Portable ZIP: PDF_to_Word_Converter_Portable.zip ({size:.1f} MB)")
    
    # Installer
    installer_dir = "installer_output"
    if os.path.exists(installer_dir):
        installer_files = list(Path(installer_dir).glob("*.exe"))
        if installer_files:
            installer_file = installer_files[0]
            size = installer_file.stat().st_size / (1024 * 1024)
            files_created.append(f"✅ Installer: {installer_file} ({size:.1f} MB)")
    
    # แสดงผลสรุป
    end_time = time.time()
    duration = end_time - start_time
    
    print("\n🎉 การ Build เสร็จสิ้น!")
    print(f"⏱️  เวลาที่ใช้: {duration:.1f} วินาที")
    print("\n📦 ไฟล์ที่สร้างขึ้น:")
    
    if files_created:
        for file_info in files_created:
            print(f"   {file_info}")
    else:
        print("   ❌ ไม่มีไฟล์ที่สร้างสำเร็จ")
        return False
    
    print("\n📋 วิธีการใช้งาน:")
    print("1. ใช้งานโดยตรง: เปิดไฟล์ dist/PDF_to_Word_Converter.exe")
    print("2. แจกจ่าย Portable: ส่งไฟล์ PDF_to_Word_Converter_Portable.zip")
    if any("Installer:" in f for f in files_created):
        print("3. ติดตั้งในระบบ: ใช้ไฟล์ installer ในโฟลเดอร์ installer_output/")
    
    print("\n📖 อ่านคู่มือเพิ่มเติม: INSTALLATION_GUIDE.md")
    
    return True


if __name__ == "__main__":
    success = main()
    if success:
        print("\n🎊 สำเร็จ! พร้อมใช้งานและแจกจ่าย")
        sys.exit(0)
    else:
        print("\n💥 มีข้อผิดพลาดเกิดขึ้น")
        sys.exit(1)
