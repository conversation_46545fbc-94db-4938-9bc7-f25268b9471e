# 📦 PDF to Word Converter - คู่มือการติดตั้งและใช้งาน

## 🎯 ภาพรวม

PDF to Word Converter เป็นโปรแกรมแปลงไฟล์ PDF เป็น Word Document (.docx) ที่ใช้งานง่าย รองรับภาษาไทย และมี GUI ที่เป็นมิตรกับผู้ใช้

## 📋 ไฟล์ที่ได้รับ

หลังจากการ build จะได้ไฟล์ดังนี้:

### 1. **Executable App** 
- `dist/PDF_to_Word_Converter.exe` - ไฟล์โปรแกรมหลัก (ขนาดประมาณ 150-200 MB)

### 2. **Portable Version**
- `PDF_to_Word_Converter_Portable.zip` - เวอร์ชันพกพา ไม่ต้องติดตั้ง

### 3. **Installer (ถ้ามี Inno Setup)**
- `installer_output/PDF_to_Word_Converter_Setup.exe` - ไฟล์ติดตั้ง

## 🚀 วิธีการใช้งาน

### แบบที่ 1: ใช้งานโดยตรง (Portable)

1. **ดาวน์โหลด**: แตกไฟล์ `PDF_to_Word_Converter_Portable.zip`
2. **เปิดโปรแกรม**: ดับเบิลคลิกที่ `PDF_to_Word_Converter.exe`
3. **เลือกไฟล์ PDF**: คลิก "เลือกไฟล์" และเลือกไฟล์ PDF ที่ต้องการแปลง
4. **เลือกโฟลเดอร์ปลายทาง**: คลิก "เลือกโฟลเดอร์" เพื่อเลือกที่บันทึกไฟล์ Word
5. **แปลงไฟล์**: คลิก "แปลงไฟล์" และรอจนเสร็จสิ้น

### แบบที่ 2: ติดตั้งในระบบ (ถ้ามี Installer)

1. **รัน Installer**: ดับเบิลคลิกที่ `PDF_to_Word_Converter_Setup.exe`
2. **ทำตามขั้นตอน**: ติดตามคำแนะนำของ installer
3. **เปิดโปรแกรม**: หาโปรแกรมใน Start Menu หรือ Desktop

## 🔧 ข้อกำหนดระบบ

- **ระบบปฏิบัติการ**: Windows 10/11 (64-bit)
- **RAM**: อย่างน้อย 4GB (แนะนำ 8GB สำหรับไฟล์ PDF ขนาดใหญ่)
- **พื้นที่ว่าง**: อย่างน้อย 500MB
- **ไม่ต้องติดตั้ง Python** - โปรแกรมมี Python runtime ในตัวแล้ว

## ✨ คุณสมบัติ

- ✅ แปลงไฟล์ PDF เป็น Word Document (.docx)
- ✅ รองรับภาษาไทยและภาษาอื่นๆ
- ✅ GUI ที่ใช้งานง่าย
- ✅ แสดงความคืบหน้าการแปลง
- ✅ Log การทำงานแบบ Real-time
- ✅ ไม่ต้องติดตั้ง dependencies เพิ่มเติม

## 🎨 หน้าตาโปรแกรม

```
┌─────────────────────────────────────────┐
│        PDF to Word Converter           │
├─────────────────────────────────────────┤
│ เลือกไฟล์ PDF: [___________] [เลือกไฟล์] │
│ โฟลเดอร์ปลายทาง: [_______] [เลือกโฟลเดอร์] │
│                                         │
│ ความคืบหน้า: [████████████████████] 100% │
│                                         │
│           [แปลงไฟล์]                     │
│                                         │
│ Log:                                    │
│ ┌─────────────────────────────────────┐ │
│ │ เลือกไฟล์: document.pdf            │ │
│ │ กำลังแปลงไฟล์...                   │ │
│ │ แปลงไฟล์สำเร็จ!                    │ │
│ └─────────────────────────────────────┘ │
└─────────────────────────────────────────┘
```

## 🔍 การแก้ไขปัญหา

### ปัญหาที่พบบ่อย

1. **โปรแกรมเปิดไม่ขึ้น**
   - ตรวจสอบว่าเป็น Windows 64-bit
   - ปิด Antivirus ชั่วคราว (บางตัวอาจบล็อก)
   - รันในโหมด Administrator

2. **แปลงไฟล์ไม่สำเร็จ**
   - ตรวจสอบว่าไฟล์ PDF ไม่มีรหัสผ่าน
   - ตรวจสอบพื้นที่ว่างในฮาร์ดดิสก์
   - ลองไฟล์ PDF ขนาดเล็กกว่า

3. **โปรแกรมช้า**
   - ไฟล์ PDF ขนาดใหญ่ใช้เวลานาน
   - ปิดโปรแกรมอื่นที่ไม่จำเป็น
   - ตรวจสอบ RAM ว่างเพียงพอ

### ข้อจำกัด

- ไฟล์ PDF ที่มีรหัสผ่านอาจแปลงไม่ได้
- การแปลงตารางซับซ้อนอาจไม่สมบูรณ์ 100%
- รูปภาพในไฟล์ PDF อาจมีคุณภาพลดลง

## 📞 การติดต่อ

หากพบปัญหาหรือต้องการความช่วยเหลือ:
- สร้าง Issue ใน GitHub Repository
- ส่งอีเมลถึงผู้พัฒนา

## 📄 License

MIT License - ใช้งานได้อย่างอิสระ

---

## 🛠️ สำหรับนักพัฒนา

### การ Build จาก Source Code

```bash
# 1. Clone repository
git clone <repository-url>
cd pdf-to-word-converter

# 2. ติดตั้ง dependencies
py install_dependencies.py

# 3. สร้าง executable
py -m PyInstaller pdf_to_word_converter.spec

# 4. สร้าง installer และ portable version
py build_installer.py
```

### โครงสร้างไฟล์

```
PDF To WOED/
├── pdf_to_word_converter.py    # โปรแกรมหลัก
├── requirements.txt            # Dependencies
├── pdf_to_word_converter.spec  # PyInstaller config
├── installer_setup.iss         # Inno Setup script
├── build_installer.py          # Build script
├── create_icon.py             # Icon generator
├── test_converter.py          # Unit tests
├── dist/                      # Executable output
├── portable_version/          # Portable files
└── installer_output/          # Installer output
```

### การปรับแต่ง

- **เปลี่ยน Icon**: แก้ไขไฟล์ `create_icon.py`
- **เปลี่ยนชื่อ App**: แก้ไขใน `installer_setup.iss`
- **เพิ่ม Features**: แก้ไขใน `pdf_to_word_converter.py`

---

**🎉 ขอให้ใช้งานอย่างมีความสุข!**
