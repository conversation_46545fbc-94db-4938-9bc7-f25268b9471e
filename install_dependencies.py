#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Dependency installer for PDF to Word Converter
ติดตั้ง dependencies สำหรับ PDF to Word Converter
"""

import subprocess
import sys
import os


def install_package(package):
    """ติดตั้ง package ผ่าน pip"""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        return True
    except subprocess.CalledProcessError:
        return False


def check_package(package):
    """ตรวจสอบว่า package ติดตั้งแล้วหรือไม่"""
    try:
        __import__(package)
        return True
    except ImportError:
        return False


def main():
    """ติดตั้ง dependencies ทั้งหมด"""
    print("PDF to Word Converter - Dependency Installer")
    print("=" * 50)
    
    # List of required packages
    packages = [
        ("pdf2docx", "pdf2docx>=0.5.8"),
        ("docx", "python-docx>=0.8.11"),
        ("PyPDF2", "PyPDF2>=3.0.1"),
    ]
    
    # Check Python version
    if sys.version_info < (3, 7):
        print("ข้อผิดพลาด: ต้องใช้ Python 3.7 หรือใหม่กว่า")
        print(f"Python version ปัจจุบัน: {sys.version}")
        sys.exit(1)
    
    print(f"Python version: {sys.version}")
    print()
    
    # Install packages
    failed_packages = []
    
    for package_name, package_spec in packages:
        print(f"ตรวจสอบ {package_name}...", end=" ")
        
        if check_package(package_name):
            print("✓ ติดตั้งแล้ว")
        else:
            print("✗ ยังไม่ติดตั้ง")
            print(f"กำลังติดตั้ง {package_spec}...", end=" ")
            
            if install_package(package_spec):
                print("✓ สำเร็จ")
            else:
                print("✗ ล้มเหลว")
                failed_packages.append(package_spec)
    
    print()
    
    # Check tkinter (usually comes with Python)
    print("ตรวจสอบ tkinter...", end=" ")
    try:
        import tkinter
        print("✓ พร้อมใช้งาน")
    except ImportError:
        print("✗ ไม่พบ tkinter")
        print("หมายเหตุ: tkinter มักจะมาพร้อมกับ Python แต่อาจต้องติดตั้งแยกใน Linux")
        print("สำหรับ Ubuntu/Debian: sudo apt-get install python3-tk")
        print("สำหรับ CentOS/RHEL: sudo yum install tkinter")
    
    print()
    
    # Summary
    if failed_packages:
        print("⚠️  การติดตั้งไม่สมบูรณ์")
        print("Packages ที่ติดตั้งไม่สำเร็จ:")
        for package in failed_packages:
            print(f"  - {package}")
        print()
        print("กรุณาลองติดตั้งด้วยตนเอง:")
        print("pip install -r requirements.txt")
    else:
        print("✅ ติดตั้ง dependencies สำเร็จทั้งหมด!")
        print()
        print("สามารถเริ่มใช้งานได้แล้ว:")
        print("python pdf_to_word_converter.py")
        print("หรือ")
        print("python run_gui.py")
    
    print()
    print("หากต้องการทดสอบการทำงาน:")
    print("python test_converter.py")


if __name__ == "__main__":
    main()
